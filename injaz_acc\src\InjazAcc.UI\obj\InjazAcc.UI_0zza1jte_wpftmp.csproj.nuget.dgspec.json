{"format": 1, "restore": {"J:\\injaz_acc\\src\\InjazAcc.UI\\InjazAcc.UI.csproj": {}}, "projects": {"J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj", "projectName": "InjazAcc.Core", "projectPath": "J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "J:\\injaz_acc\\src\\InjazAcc.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "J:\\injaz_acc\\src\\InjazAcc.DataAccess\\InjazAcc.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "J:\\injaz_acc\\src\\InjazAcc.DataAccess\\InjazAcc.DataAccess.csproj", "projectName": "InjazAcc.DataAccess", "projectPath": "J:\\injaz_acc\\src\\InjazAcc.DataAccess\\InjazAcc.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "J:\\injaz_acc\\src\\InjazAcc.DataAccess\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj": {"projectPath": "J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "J:\\injaz_acc\\src\\InjazAcc.Services\\InjazAcc.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "J:\\injaz_acc\\src\\InjazAcc.Services\\InjazAcc.Services.csproj", "projectName": "InjazAcc.Services", "projectPath": "J:\\injaz_acc\\src\\InjazAcc.Services\\InjazAcc.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "J:\\injaz_acc\\src\\InjazAcc.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj": {"projectPath": "J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj"}, "J:\\injaz_acc\\src\\InjazAcc.DataAccess\\InjazAcc.DataAccess.csproj": {"projectPath": "J:\\injaz_acc\\src\\InjazAcc.DataAccess\\InjazAcc.DataAccess.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "J:\\injaz_acc\\src\\InjazAcc.UI\\InjazAcc.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "J:\\injaz_acc\\src\\InjazAcc.UI\\InjazAcc.UI.csproj", "projectName": "InjazAcc.UI", "projectPath": "J:\\injaz_acc\\src\\InjazAcc.UI\\InjazAcc.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "J:\\injaz_acc\\src\\InjazAcc.UI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj": {"projectPath": "J:\\injaz_acc\\src\\InjazAcc.Core\\InjazAcc.Core.csproj"}, "J:\\injaz_acc\\src\\InjazAcc.Services\\InjazAcc.Services.csproj": {"projectPath": "J:\\injaz_acc\\src\\InjazAcc.Services\\InjazAcc.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}