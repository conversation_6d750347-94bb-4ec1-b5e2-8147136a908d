using System;
using System.Data.SQLite;
using System.IO;

class DatabaseViewer
{
    static void Main(string[] args)
    {
        string dbPath = "InjazAccDb.db";
        
        // البحث عن ملف قاعدة البيانات في المواقع المختلفة
        string[] possiblePaths = {
            dbPath,
            Path.Combine("src", "InjazAcc.UI", dbPath),
            Path.Combine("injaz_acc", dbPath),
            Path.Combine("injaz_acc", "src", "InjazAcc.UI", dbPath)
        };
        
        string actualDbPath = null;
        foreach (string path in possiblePaths)
        {
            if (File.Exists(path))
            {
                actualDbPath = path;
                break;
            }
        }
        
        if (actualDbPath == null)
        {
            Console.WriteLine("لم يتم العثور على ملف قاعدة البيانات");
            return;
        }
        
        Console.WriteLine($"تم العثور على قاعدة البيانات في: {actualDbPath}");
        Console.WriteLine($"حجم الملف: {new FileInfo(actualDbPath).Length} بايت");
        Console.WriteLine();
        
        string connectionString = $"Data Source={actualDbPath};Version=3;";
        
        try
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // عرض الجداول الموجودة
                Console.WriteLine("الجداول الموجودة في قاعدة البيانات:");
                Console.WriteLine("=====================================");
                
                string tablesQuery = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;";
                using (var command = new SQLiteCommand(tablesQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        string tableName = reader.GetString(0);
                        Console.WriteLine($"- {tableName}");
                        
                        // عد الصفوف في كل جدول
                        string countQuery = $"SELECT COUNT(*) FROM [{tableName}];";
                        using (var countCommand = new SQLiteCommand(countQuery, connection))
                        {
                            try
                            {
                                long count = (long)countCommand.ExecuteScalar();
                                Console.WriteLine($"  عدد الصفوف: {count}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"  خطأ في عد الصفوف: {ex.Message}");
                            }
                        }
                    }
                }
                
                Console.WriteLine();
                
                // عرض بيانات المستخدمين إذا وجدت
                Console.WriteLine("بيانات المستخدمين:");
                Console.WriteLine("==================");
                
                string usersQuery = "SELECT * FROM Users LIMIT 10;";
                using (var command = new SQLiteCommand(usersQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    if (reader.HasRows)
                    {
                        while (reader.Read())
                        {
                            Console.WriteLine($"ID: {reader["Id"]}, Username: {reader["Username"]}, FullName: {reader["FullName"]}, Email: {reader["Email"]}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("لا توجد بيانات مستخدمين");
                    }
                }
                
                Console.WriteLine();
                
                // عرض بيانات العملاء إذا وجدت
                Console.WriteLine("بيانات العملاء:");
                Console.WriteLine("===============");
                
                string customersQuery = "SELECT * FROM Customers LIMIT 10;";
                using (var command = new SQLiteCommand(customersQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    if (reader.HasRows)
                    {
                        while (reader.Read())
                        {
                            Console.WriteLine($"ID: {reader["Id"]}, Name: {reader["Name"]}, Phone: {reader["Phone"]}, Email: {reader["Email"]}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("لا توجد بيانات عملاء");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}");
        }
        
        Console.WriteLine();
        Console.WriteLine("اضغط أي مفتاح للخروج...");
        Console.ReadKey();
    }
}
